#pragma once
#include <cstdint>

// Memory offsets and addresses from the UnknownCheats thread
// Format: s2_mp64_ship.exe + offset

namespace Offsets {
    // Player data
    constexpr uintptr_t PLAYER_BASE = 0x0A2D7DC8;
    constexpr uintptr_t HEALTH_OFFSET = 0x2DC;
    constexpr uintptr_t USERNAME_BASE = 0x0E4650B0;
    constexpr uintptr_t USERNAME_OFFSET = 0x144;
    
    // Position offsets
    constexpr uintptr_t POSITION_BASE = 0x0A0C7388;
    constexpr uintptr_t X_POSITION_OFFSET = 0x88;
    constexpr uintptr_t Y_POSITION_OFFSET = 0x8C;
    constexpr uintptr_t Z_POSITION_OFFSET = 0x84;
    
    // Special abilities and equipment
    constexpr uintptr_t SPECIAL_ABILITY = 0x0A4B1888;
    constexpr uintptr_t LETHALS = 0x768;
    
    // Weapon addresses (relative to POSITION_BASE)
    constexpr uintptr_t PISTOL_1 = 0x784;
    constexpr uintptr_t PISTOL_2 = 0x780;
    constexpr uintptr_t M1_GARAND = 0x7C8;
    constexpr uintptr_t MACHINE_PISTOL_1 = 0x7B4;
    constexpr uintptr_t MACHINE_PISTOL_2 = 0x7B0;
    constexpr uintptr_t M30_DRILLING = 0x7B0;
    constexpr uintptr_t TYPE_100 = 0x7C8;
    constexpr uintptr_t SVT_40 = 0x7B0;
    constexpr uintptr_t M1928 = 0x7C8;
    
    // Current weapon info
    constexpr uintptr_t CURRENT_WEAPON_AMMO = 0x10948C8;
    constexpr uintptr_t CURRENT_WEAPON_AMMO_OFFSET = 0x8;
    constexpr uintptr_t CURRENT_WEAPON_ADDRESS = 0x97863808; // Long integer address
    
    // Entity system (for aimbot)
    constexpr uintptr_t ZOMBIE_ARRAY = 0x0A0D35B0;
    constexpr uintptr_t ENTITY_SIZE = 0x418;
    constexpr uintptr_t MAX_ENTITIES = 32;
    
    // RefDef for world-to-screen conversion
    constexpr uintptr_t REFDEF_POINTER = 0x8CE9968;
    
    // Cheat Engine detection
    constexpr uintptr_t CE_BLOCKER_MP = 0x226670;
    constexpr uintptr_t CE_BLOCKER_SP = 0x5B3D0;
    
    // Game state
    constexpr uintptr_t ROUND_NUMBER = 0x0A2D7DC8; // Need to find correct offset
    constexpr uintptr_t POINTS_ADDRESS = 0x0A2D7DC8; // Need to find correct offset
    
    // Weapon IDs (common zombies weapons)
    namespace WeaponIDs {
        constexpr int M1911 = 1;
        constexpr int M1_GARAND = 2;
        constexpr int M1928_THOMPSON = 3;
        constexpr int STG44 = 4;
        constexpr int MP40 = 5;
        constexpr int TYPE_100 = 6;
        constexpr int PPSH41 = 7;
        constexpr int M30_DRILLING = 8;
        constexpr int COMBAT_SHOTGUN = 9;
        constexpr int TOGGLE_ACTION = 10;
        constexpr int M1903 = 11;
        constexpr int KARABIN = 12;
        constexpr int LEE_ENFIELD = 13;
        constexpr int SVT40 = 14;
        constexpr int GEWEHR_43 = 15;
        constexpr int M1_CARBINE = 16;
        constexpr int BAR = 17;
        constexpr int BREN = 18;
        constexpr int FG42 = 19;
        constexpr int MG15 = 20;
        constexpr int MG42 = 21;
        constexpr int LEWIS = 22;
        constexpr int GPMG = 23;
        constexpr int MG81 = 24;
        constexpr int PANZERSCHRECK = 25;
        constexpr int BAZOOKA = 26;
        constexpr int PANZERKNACKER = 27;
        constexpr int FLAMMENWERFER = 28;
        constexpr int WUNDERWAFFE_DG2 = 100; // Wonder weapon
        constexpr int TESLA_GUN = 101;
        constexpr int BRENNER_HEAD = 102;
        constexpr int RIPSAW = 103;
        constexpr int MIDNIGHT = 104;
    }
    
    // Perk IDs
    namespace PerkIDs {
        constexpr int JUGGERNOG = 1;
        constexpr int SPEED_COLA = 2;
        constexpr int DOUBLE_TAP = 3;
        constexpr int QUICK_REVIVE = 4;
        constexpr int STAMIN_UP = 5;
        constexpr int DEADSHOT_DAIQUIRI = 6;
        constexpr int MULE_KICK = 7;
        constexpr int PHD_FLOPPER = 8;
        constexpr int ELECTRIC_CHERRY = 9;
        constexpr int VULTURE_AID = 10;
        constexpr int WIDOWS_WINE = 11;
    }
}

// Entity structure based on the thread information
struct Entity {
    short ClientNum;                    // 0x00
    char pad_0000[146];                // 0x02
    short WeaponID;                    // 0x94
    char pad_0001[388];                // 0x96
    float vOrigin[3];                  // 0x21C (X, Y, Z position)
    char pad_0002[12];                 // 0x228
    float HeadPos[3];                  // 0x234 (Head position for aimbot)
    char pad_0003[156];                // 0x240
    int Health;                        // 0x2DC
    char pad_0004[312];                // 0x2E0
}; // Size: 0x418

// RefDef structure for world-to-screen conversion
struct RefDef {
    char pad_0000[32];                 // 0x0000
    float tanHalfFovX;                 // 0x0020
    float tanHalfFovY;                 // 0x0024
    char pad_0028[4];                  // 0x0028
    float fovX;                        // 0x002C
    float FovY;                        // 0x0030
    float Origin[3];                   // 0x0034 (Camera origin)
    float xaxis[3];                    // 0x0040
    float yaxis[3];                    // 0x004C
    float zaxis[3];                    // 0x0058
    float zNear1;                      // 0x0064
    float zNear2;                      // 0x0068
    float viewoffset1[3];              // 0x006C
    float viewoffset2[3];              // 0x0078
}; // Size: 0x0084

// Vector3 structure for 3D calculations
struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vector3 operator-(const Vector3& other) const {
        return Vector3(x - other.x, y - other.y, z - other.z);
    }
    
    Vector3 operator+(const Vector3& other) const {
        return Vector3(x + other.x, y + other.y, z + other.z);
    }
    
    float DotProduct(const Vector3& other) const {
        return x * other.x + y * other.y + z * other.z;
    }
    
    float Length() const {
        return sqrt(x * x + y * y + z * z);
    }
    
    Vector3 Normalize() const {
        float len = Length();
        if (len == 0) return Vector3();
        return Vector3(x / len, y / len, z / len);
    }
};

// Vector2 structure for 2D screen coordinates
struct Vector2 {
    float x, y;
    
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
};
