@echo off
echo ========================================
echo COD WWII Zombies Trainer Build Script
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on build.bat and select "Run as administrator"
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" mkdir build
cd build

echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorLevel% neq 0 (
    echo ERROR: CMake configuration failed!
    echo Make sure you have Visual Studio 2022 installed with C++ development tools.
    pause
    exit /b 1
)

echo.
echo Building project in Release mode...
cmake --build . --config Release
if %errorLevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: build\bin\Release\COD_WWII_Zombies_Trainer.exe
echo.
echo To use the trainer:
echo 1. Launch Call of Duty WWII
echo 2. Enter Zombies mode
echo 3. Run the trainer as Administrator
echo 4. Use F1-F12 hotkeys to activate features
echo.
pause
