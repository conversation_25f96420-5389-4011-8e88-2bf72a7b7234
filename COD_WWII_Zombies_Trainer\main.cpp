#include <Windows.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <string>
#include <TlHelp32.h>
#include <cmath>

#include "Memory.h"
#include "Trainer.h"
#include "Offsets.h"

// Console colors for better UI
#define COLOR_RED     12
#define COLOR_GREEN   10
#define COLOR_YELLOW  14
#define COLOR_WHITE   15
#define COLOR_CYAN    11

void SetConsoleColor(int color) {
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), color);
}

void PrintBanner() {
    SetConsoleColor(COLOR_CYAN);
    std::cout << R"(
    ╔══════════════════════════════════════════════════════════════╗
    ║                COD WWII ZOMBIES ULTIMATE TRAINER             ║
    ║                        Version 1.0                          ║
    ║                   Created for Solo Play                     ║
    ╚══════════════════════════════════════════════════════════════╝
    )" << std::endl;
    SetConsoleColor(COLOR_WHITE);
}

void PrintControls() {
    SetConsoleColor(COLOR_YELLOW);
    std::cout << "\n=== HOTKEY CONTROLS ===" << std::endl;
    SetConsoleColor(COLOR_WHITE);
    std::cout << "F1  - Toggle God Mode" << std::endl;
    std::cout << "F2  - Toggle Infinite Ammo" << std::endl;
    std::cout << "F3  - Toggle Rapid Fire" << std::endl;
    std::cout << "F4  - Toggle No Recoil" << std::endl;
    std::cout << "F5  - Toggle Aimbot" << std::endl;
    std::cout << "F6  - Give All Perks" << std::endl;
    std::cout << "F7  - Infinite Points" << std::endl;
    std::cout << "F8  - Pack-a-Punch Current Weapon" << std::endl;
    std::cout << "F9  - Advance Round" << std::endl;
    std::cout << "F10 - Freeze Round" << std::endl;
    std::cout << "F11 - Teleport to Spawn" << std::endl;
    std::cout << "F12 - Toggle All Features" << std::endl;
    std::cout << "INSERT - Toggle No Fall Damage" << std::endl;
    std::cout << "DELETE - Toggle Super Jump" << std::endl;
    std::cout << "END - Exit Trainer" << std::endl;
    SetConsoleColor(COLOR_YELLOW);
    std::cout << "========================" << std::endl;
    SetConsoleColor(COLOR_WHITE);
}

void PrintStatus(const std::string& feature, bool enabled) {
    SetConsoleColor(enabled ? COLOR_GREEN : COLOR_RED);
    std::cout << "[" << (enabled ? "ON" : "OFF") << "] ";
    SetConsoleColor(COLOR_WHITE);
    std::cout << feature << std::endl;
}

int main() {
    // Set console title and properties
    SetConsoleTitle(L"COD WWII Zombies Ultimate Trainer v1.0");
    
    PrintBanner();
    
    // Initialize memory management
    Memory memory;
    Trainer trainer(&memory);
    
    SetConsoleColor(COLOR_YELLOW);
    std::cout << "\nSearching for Call of Duty WWII..." << std::endl;
    SetConsoleColor(COLOR_WHITE);
    
    // Attach to game process
    if (!memory.AttachToProcess(L"s2_mp64_ship.exe")) {
        SetConsoleColor(COLOR_RED);
        std::cout << "Error: Could not find Call of Duty WWII process!" << std::endl;
        std::cout << "Make sure the game is running and you're in Zombies mode." << std::endl;
        SetConsoleColor(COLOR_WHITE);
        std::cout << "\nPress any key to exit...";
        std::cin.get();
        return 1;
    }
    
    SetConsoleColor(COLOR_GREEN);
    std::cout << "Successfully attached to Call of Duty WWII!" << std::endl;
    SetConsoleColor(COLOR_WHITE);
    
    // Initialize trainer
    if (!trainer.Initialize()) {
        SetConsoleColor(COLOR_RED);
        std::cout << "Error: Failed to initialize trainer!" << std::endl;
        SetConsoleColor(COLOR_WHITE);
        std::cout << "\nPress any key to exit...";
        std::cin.get();
        return 1;
    }
    
    SetConsoleColor(COLOR_GREEN);
    std::cout << "Trainer initialized successfully!" << std::endl;
    SetConsoleColor(COLOR_WHITE);
    
    PrintControls();
    
    std::cout << "\nTrainer is ready! Press hotkeys to toggle features." << std::endl;
    std::cout << "Status will be displayed below:\n" << std::endl;
    
    // Main loop
    bool running = true;
    while (running) {
        // Check for hotkeys
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            trainer.ToggleGodMode();
            PrintStatus("God Mode", trainer.IsGodModeEnabled());
            Sleep(200); // Prevent key spam
        }
        
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            trainer.ToggleInfiniteAmmo();
            PrintStatus("Infinite Ammo", trainer.IsInfiniteAmmoEnabled());
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            trainer.ToggleRapidFire();
            PrintStatus("Rapid Fire", trainer.IsRapidFireEnabled());
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F4) & 0x8000) {
            trainer.ToggleNoRecoil();
            PrintStatus("No Recoil", trainer.IsNoRecoilEnabled());
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F5) & 0x8000) {
            trainer.ToggleAimbot();
            PrintStatus("Aimbot", trainer.IsAimbotEnabled());
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F6) & 0x8000) {
            trainer.GiveAllPerks();
            SetConsoleColor(COLOR_GREEN);
            std::cout << "[ACTIVATED] All Perks Given!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F7) & 0x8000) {
            trainer.GiveInfinitePoints();
            SetConsoleColor(COLOR_GREEN);
            std::cout << "[ACTIVATED] Infinite Points!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F8) & 0x8000) {
            trainer.PackAPunchCurrentWeapon();
            SetConsoleColor(COLOR_GREEN);
            std::cout << "[ACTIVATED] Pack-a-Punch Applied!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F9) & 0x8000) {
            trainer.AdvanceRound();
            SetConsoleColor(COLOR_GREEN);
            std::cout << "[ACTIVATED] Round Advanced!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F10) & 0x8000) {
            trainer.ToggleFreezeRound();
            PrintStatus("Freeze Round", trainer.IsRoundFrozen());
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F11) & 0x8000) {
            trainer.TeleportToSpawn();
            SetConsoleColor(COLOR_GREEN);
            std::cout << "[ACTIVATED] Teleported to Spawn!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F12) & 0x8000) {
            trainer.ToggleAllFeatures();
            SetConsoleColor(COLOR_CYAN);
            std::cout << "[TOGGLED] All Features!" << std::endl;
            SetConsoleColor(COLOR_WHITE);
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_INSERT) & 0x8000) {
            trainer.ToggleNoFallDamage();
            PrintStatus("No Fall Damage", trainer.IsNoFallDamageEnabled());
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_DELETE) & 0x8000) {
            trainer.ToggleSuperJump();
            PrintStatus("Super Jump", trainer.IsSuperJumpEnabled());
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_END) & 0x8000) {
            running = false;
        }
        
        // Update trainer (for continuous features like aimbot)
        trainer.Update();
        
        // Small delay to prevent high CPU usage
        Sleep(10);
    }
    
    SetConsoleColor(COLOR_YELLOW);
    std::cout << "\nTrainer shutting down..." << std::endl;
    SetConsoleColor(COLOR_WHITE);
    
    return 0;
}
