# Call of Duty WWII Zombies Ultimate Trainer

A comprehensive trainer for Call of Duty WWII Zombies mode designed for solo gameplay enhancement.

## Features

### Core Combat Features
- **Aimbot** - Configurable auto-aim with FOV and smoothness settings
- **God Mode** - Infinite health protection
- **Infinite Ammo** - Never run out of ammunition
- **Rapid Fire** - Dramatically increased fire rate for all weapons
- **No Recoil** - Perfect weapon accuracy

### Weapon & Equipment Management
- **Weapon Spawning** - Give yourself any weapon by ID
- **Pack-a-Punch** - Instantly upgrade current weapon to maximum level
- **All Perks** - Instantly receive all available perks (Juggernog, Speed Cola, etc.)
- **Infinite Grenades** - Unlimited tactical equipment
- **Weapon Attachments** - Unlock and equip all attachments

### Game Progression & Economy
- **Infinite Points** - Maximum money for purchases
- **Round Control** - Advance or freeze zombie rounds
- **Zombie Modification** - Control zombie health and spawn rates
- **Teleportation** - Quick map navigation system

## System Requirements

- Windows 10/11 (64-bit)
- Call of Duty WWII (Steam version)
- Visual Studio 2019/2022 or compatible C++ compiler
- Administrator privileges (required for memory access)

## Installation & Usage

### Option 1: Pre-built Release
1. Download the latest release from the releases page
2. Extract to any folder
3. Run `COD_WWII_Zombies_Trainer.exe` as Administrator
4. Launch Call of Duty WWII and enter Zombies mode
5. Use hotkeys to activate features

### Option 2: Build from Source
1. Clone or download this repository
2. Install Visual Studio 2019/2022 with C++ development tools
3. Open Command Prompt as Administrator
4. Navigate to the project directory
5. Run the build script:
   ```batch
   build.bat
   ```
6. The executable will be created in `build/bin/`

## Hotkey Controls

| Key | Function |
|-----|----------|
| F1  | Toggle God Mode |
| F2  | Toggle Infinite Ammo |
| F3  | Toggle Rapid Fire |
| F4  | Toggle No Recoil |
| F5  | Toggle Aimbot |
| F6  | Give All Perks |
| F7  | Infinite Points |
| F8  | Pack-a-Punch Current Weapon |
| F9  | Advance Round |
| F10 | Freeze Round |
| F11 | Teleport to Spawn |
| F12 | Toggle All Features |
| END | Exit Trainer |

## Configuration

### Aimbot Settings
The aimbot can be configured by modifying the following values in the source code:
- **FOV**: Field of view for target detection (default: 60°)
- **Smoothness**: Aiming smoothness factor (default: 5.0)
- **Headshots**: Target zombie heads for maximum damage (default: enabled)

### Weapon IDs
Common weapon IDs for spawning:
- M1911 Pistol: 1
- M1 Garand: 2
- Thompson M1928: 3
- STG44: 4
- MP40: 5
- PPSH-41: 7
- Wunderwaffe DG-2: 100

## Technical Details

This trainer uses memory manipulation techniques to modify game values in real-time. It includes:

- **Process Attachment**: Automatically finds and attaches to the COD WWII process
- **Memory Management**: Safe reading and writing of game memory
- **Cheat Engine Bypass**: Attempts to bypass anti-cheat detection
- **Entity System**: Advanced zombie detection and targeting
- **World-to-Screen**: 3D to 2D coordinate conversion for aimbot

## Safety & Compatibility

- **Steam Version Only**: This trainer is designed specifically for the Steam version of COD WWII
- **Zombies Mode**: Optimized for Zombies mode (may not work properly in Campaign/Multiplayer)
- **Solo Play**: Intended for solo gameplay only
- **No Ban Risk**: COD WWII has no active anti-cheat for solo play

## Troubleshooting

### Common Issues

**"Could not find Call of Duty WWII process"**
- Ensure the game is running
- Make sure you're using the Steam version
- Run the trainer as Administrator

**"Failed to initialize trainer"**
- Close and restart both the game and trainer
- Verify game integrity through Steam
- Check Windows Defender/antivirus exclusions

**Features not working**
- Some features may require specific game states
- Try toggling features off and on again
- Restart the trainer if issues persist

### Memory Addresses
The trainer uses memory addresses discovered through reverse engineering. If a game update breaks functionality, the addresses in `Offsets.h` may need to be updated.

## Building Requirements

- CMake 3.16 or higher
- Visual Studio 2019/2022 with MSVC compiler
- Windows SDK 10.0 or higher

## Disclaimer

This trainer is for educational and solo gameplay purposes only. Use at your own risk. The developers are not responsible for any issues that may arise from using this software.

## Credits

- Memory addresses and techniques based on research from the UnknownCheats community
- Special thanks to suspectedesp for the comprehensive COD WWII thread
- Built with modern C++ and Windows API

## License

This project is released under the MIT License. See LICENSE.txt for details.
