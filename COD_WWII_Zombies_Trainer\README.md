# Call of Duty WWII Zombies Ultimate Trainer

A comprehensive trainer for Call of Duty WWII Zombies mode designed for solo gameplay enhancement with full Xbox controller support.

## Features

### Core Combat Features (Xbox Controller Optimized)
- **Advanced Aimbot** - Xbox controller optimized with auto-aim, wider FOV, and trigger-based activation
- **God Mode** - Infinite health protection
- **Infinite Ammo** - Never run out of ammunition
- **Rapid Fire** - Dramatically increased fire rate (enhanced for controller)
- **No Recoil** - Perfect weapon accuracy
- **Auto-Fire** - Automatic firing when aiming with controller triggers

### Weapon & Equipment Management
- **Weapon Spawning** - Give yourself any weapon by ID
- **Pack-a-Punch** - Instantly upgrade current weapon to maximum level
- **All Perks** - Instantly receive all available perks (Juggernog, Speed Cola, etc.)
- **Infinite Grenades** - Unlimited tactical equipment
- **Weapon Attachments** - Unlock and equip all attachments

### Game Progression & Economy
- **Infinite Points** - Maximum money for purchases
- **Round Control** - Advance or freeze zombie rounds
- **Zombie Modification** - Control zombie health and spawn rates
- **Teleportation** - Quick map navigation system

## System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Game**: Call of Duty WWII (Steam version only)
- **Controller**: Xbox One/Series X|S controller (wired or wireless)
- **Development**: Visual Studio 2019/2022 with C++ tools (for building)
- **Privileges**: Administrator access (required for memory manipulation)
- **Dependencies**: Visual C++ Redistributable 2019/2022

## Installation & Usage

### 🚀 Quick Setup (Recommended)
1. **Download** this trainer package
2. **Right-click** on `QUICK_SETUP.bat` and select **"Run as administrator"**
3. **Follow the automated setup** - it will check requirements and build the trainer
4. **Connect your Xbox controller** to PC (USB or Bluetooth)
5. **Launch Call of Duty WWII** and enter Zombies mode
6. **Run the trainer** as Administrator

### 🛠️ Manual Build (Advanced Users)
1. **Install Visual Studio 2019/2022** with C++ development tools
2. **Install CMake** from https://cmake.org/download/
3. **Open Command Prompt as Administrator**
4. **Navigate to project directory**
5. **Run build script**:
   ```batch
   build.bat
   ```
6. **Executable created** in `build/bin/Release/`

### 🎮 Xbox Controller Setup
1. **Connect controller** via USB or pair via Bluetooth
2. **Verify in Windows** - Controller should appear in Device Manager
3. **Test in game** - Controller should work normally in COD WWII
4. **Launch trainer** - Look for "Xbox Controller detected" message

## Controls

### Keyboard Controls
| Key | Function |
|-----|----------|
| F1  | Toggle God Mode |
| F2  | Toggle Infinite Ammo |
| F3  | Toggle Rapid Fire |
| F4  | Toggle No Recoil |
| F5  | Toggle Aimbot |
| F6  | Give All Perks |
| F7  | Infinite Points |
| F8  | Pack-a-Punch Current Weapon |
| F9  | Advance Round |
| F10 | Freeze Round |
| F11 | Teleport to Spawn |
| F12 | Toggle All Features |
| INSERT | Toggle No Fall Damage |
| DELETE | Toggle Super Jump |
| END | Exit Trainer |

### Xbox Controller Controls (Optimized)
| Button | Function |
|--------|----------|
| D-Pad Up | Toggle God Mode |
| D-Pad Down | Toggle Infinite Ammo |
| D-Pad Left | Toggle Rapid Fire |
| D-Pad Right | Toggle No Recoil |
| LB (L1) | Toggle Aimbot |
| RB (R1) | Give All Perks |
| Y | Infinite Points |
| X | Pack-a-Punch Current Weapon |
| B | Toggle No Fall Damage |
| A | Toggle Super Jump |
| Left Stick Click | Teleport to Spawn |
| Right Stick Click | Toggle All Features |
| Start | Advance Round |
| Back/Select | Freeze Round |
| **LT (Aim) + RT (Fire)** | **Aimbot + Auto-Fire** |

## Configuration

### Aimbot Settings
The aimbot can be configured by modifying the following values in the source code:
- **FOV**: Field of view for target detection (default: 60°)
- **Smoothness**: Aiming smoothness factor (default: 5.0)
- **Headshots**: Target zombie heads for maximum damage (default: enabled)

### Weapon IDs
Common weapon IDs for spawning:
- M1911 Pistol: 1
- M1 Garand: 2
- Thompson M1928: 3
- STG44: 4
- MP40: 5
- PPSH-41: 7
- Wunderwaffe DG-2: 100

## Technical Details

This trainer uses memory manipulation techniques to modify game values in real-time. It includes:

- **Process Attachment**: Automatically finds and attaches to the COD WWII process
- **Memory Management**: Safe reading and writing of game memory
- **Cheat Engine Bypass**: Attempts to bypass anti-cheat detection
- **Entity System**: Advanced zombie detection and targeting
- **World-to-Screen**: 3D to 2D coordinate conversion for aimbot

## Safety & Compatibility

- **Steam Version Only**: This trainer is designed specifically for the Steam version of COD WWII
- **Zombies Mode**: Optimized for Zombies mode (may not work properly in Campaign/Multiplayer)
- **Solo Play**: Intended for solo gameplay only
- **No Ban Risk**: COD WWII has no active anti-cheat for solo play

## Troubleshooting

### Common Issues

**"Could not find Call of Duty WWII process"**
- Ensure the game is running
- Make sure you're using the Steam version
- Run the trainer as Administrator

**"Failed to initialize trainer"**
- Close and restart both the game and trainer
- Verify game integrity through Steam
- Check Windows Defender/antivirus exclusions

**Features not working**
- Some features may require specific game states
- Try toggling features off and on again
- Restart the trainer if issues persist

### Memory Addresses
The trainer uses memory addresses discovered through reverse engineering. If a game update breaks functionality, the addresses in `Offsets.h` may need to be updated.

## Building Requirements

- CMake 3.16 or higher
- Visual Studio 2019/2022 with MSVC compiler
- Windows SDK 10.0 or higher

## Disclaimer

This trainer is for educational and solo gameplay purposes only. Use at your own risk. The developers are not responsible for any issues that may arise from using this software.

## Credits

- Memory addresses and techniques based on research from the UnknownCheats community
- Special thanks to suspectedesp for the comprehensive COD WWII thread
- Built with modern C++ and Windows API

## License

This project is released under the MIT License. See LICENSE.txt for details.
