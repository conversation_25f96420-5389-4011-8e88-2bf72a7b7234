#pragma once
#include "Memory.h"
#include "Offsets.h"
#include "XboxController.h"
#include <vector>
#include <chrono>

class Trainer {
private:
    Memory* memory;
    XboxController* controller;
    
    // Feature states
    bool godModeEnabled;
    bool infiniteAmmoEnabled;
    bool rapidFireEnabled;
    bool noRecoilEnabled;
    bool aimbotEnabled;
    bool roundFrozen;
    bool allFeaturesEnabled;
    bool noFallDamageEnabled;
    bool superJumpEnabled;
    
    // Aimbot settings (optimized for Xbox controller)
    float aimbotFOV;
    float aimbotSmoothness;
    bool aimbotHeadshots;
    float controllerSensitivity;
    bool autoFireEnabled;
    float rapidFireRate;
    
    // Cached addresses
    uintptr_t playerHealthAddress;
    uintptr_t playerPositionAddress;
    uintptr_t currentWeaponAmmoAddress;
    uintptr_t specialAbilityAddress;
    uintptr_t refDefAddress;
    uintptr_t jumpHeightAddress;
    uintptr_t fallDamageAddress;
    uintptr_t velocityAddress;
    uintptr_t gravityAddress;
    
    // Original values for restoration
    int originalHealth;
    int originalAmmo;
    
    // Timing for smooth operations
    std::chrono::steady_clock::time_point lastAimbotUpdate;
    std::chrono::steady_clock::time_point lastRapidFireUpdate;
    
public:
    Trainer(Memory* mem);
    ~Trainer();
    
    // Initialization
    bool Initialize();
    void Update();
    
    // Core combat features
    void ToggleGodMode();
    void ToggleInfiniteAmmo();
    void ToggleRapidFire();
    void ToggleNoRecoil();
    void ToggleAimbot();

    // Movement features
    void ToggleNoFallDamage();
    void ToggleSuperJump();
    void SetJumpHeight(float multiplier);
    void SetGravityMultiplier(float multiplier);
    
    // Weapon and equipment management
    void GiveWeapon(int weaponID);
    void PackAPunchCurrentWeapon();
    void GiveAllPerks();
    void GiveInfiniteGrenades();
    void UnlockAllAttachments();
    
    // Game progression and economy
    void GiveInfinitePoints();
    void AdvanceRound();
    void ToggleFreezeRound();
    void ModifyZombieHealth(int healthMultiplier);
    void ModifyZombieSpawnRate(float rateMultiplier);
    
    // Teleportation system
    void TeleportToSpawn();
    void TeleportToPosition(float x, float y, float z);
    void SaveCurrentPosition();
    void LoadSavedPosition();
    
    // Utility functions
    void ToggleAllFeatures();
    void RestoreOriginalValues();
    
    // Getters for status
    bool IsGodModeEnabled() const { return godModeEnabled; }
    bool IsInfiniteAmmoEnabled() const { return infiniteAmmoEnabled; }
    bool IsRapidFireEnabled() const { return rapidFireEnabled; }
    bool IsNoRecoilEnabled() const { return noRecoilEnabled; }
    bool IsAimbotEnabled() const { return aimbotEnabled; }
    bool IsRoundFrozen() const { return roundFrozen; }
    bool IsNoFallDamageEnabled() const { return noFallDamageEnabled; }
    bool IsSuperJumpEnabled() const { return superJumpEnabled; }
    
    // Aimbot configuration (Xbox controller optimized)
    void SetAimbotFOV(float fov) { aimbotFOV = fov; }
    void SetAimbotSmoothness(float smoothness) { aimbotSmoothness = smoothness; }
    void SetAimbotHeadshots(bool enabled) { aimbotHeadshots = enabled; }
    void SetControllerSensitivity(float sensitivity) { controllerSensitivity = sensitivity; }
    void SetAutoFire(bool enabled) { autoFireEnabled = enabled; }
    void SetRapidFireRate(float rate) { rapidFireRate = rate; }

    // Xbox controller support
    bool IsControllerConnected() const;
    void UpdateControllerInput();
    void ProcessControllerAimbot();
    void ProcessControllerFeatures();
    void AimAtTargetController(const Vector3& targetPos);
    void TriggerAutoFire();
    
private:
    // Internal helper functions
    bool CacheAddresses();
    void UpdateGodMode();
    void UpdateInfiniteAmmo();
    void UpdateRapidFire();
    void UpdateNoRecoil();
    void UpdateAimbot();
    void UpdateNoFallDamage();
    void UpdateSuperJump();
    
    // Aimbot helper functions
    std::vector<Entity> GetNearbyZombies();
    Entity* FindBestTarget(const std::vector<Entity>& zombies);
    Vector2 WorldToScreen(const Vector3& worldPos);
    float GetDistanceToTarget(const Vector3& playerPos, const Vector3& targetPos);
    void AimAtTarget(const Vector3& targetPos);
    
    // Memory helper functions
    Vector3 GetPlayerPosition();
    Vector3 GetPlayerViewAngles();
    void SetPlayerViewAngles(const Vector3& angles);
    RefDef GetRefDef();
    
    // Weapon helper functions
    int GetCurrentWeaponID();
    void SetCurrentWeaponAmmo(int ammo);
    void SetWeaponFireRate(float rate);
    void SetWeaponRecoil(float recoil);
    
    // Game state helper functions
    int GetCurrentRound();
    void SetCurrentRound(int round);
    int GetPlayerPoints();
    void SetPlayerPoints(int points);
    void SetPlayerHealth(int health);
    
    // Perk helper functions
    void GivePerk(int perkID);
    bool HasPerk(int perkID);
    
    // Position helper functions
    Vector3 savedPosition;
    bool hasSavedPosition;
};
