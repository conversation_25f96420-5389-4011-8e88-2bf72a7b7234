<?xml version="1.0" encoding="utf-8"?>
<CheatTable>
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"=== COD WWII ZOMBIES ULTIMATE TRAINER ==="</Description>
      <Options moHideChildren="1"/>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>
      </AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>1</ID>
          <Description>"God Mode"</Description>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>[ENABLE]
s2_mp64_ship.exe+A2D7DC8:
db 90 90 90 90 90 90

[DISABLE]
s2_mp64_ship.exe+A2D7DC8:
db 29 87 DC 02 00 00
          </AssemblerScript>
        </CheatEntry>
        <CheatEntry>
          <ID>2</ID>
          <Description>"Infinite Health"</Description>
          <LastState/>
          <VariableType>4 Bytes</VariableType>
          <Address>s2_mp64_ship.exe+A2D7DC8</Address>
          <Offsets>
            <Offset>2DC</Offset>
          </Offsets>
          <Value>999999</Value>
        </CheatEntry>
        <CheatEntry>
          <ID>3</ID>
          <Description>"Infinite Ammo - Current Weapon"</Description>
          <LastState/>
          <VariableType>4 Bytes</VariableType>
          <Address>s2_mp64_ship.exe+10948C8</Address>
          <Offsets>
            <Offset>8</Offset>
          </Offsets>
          <Value>999</Value>
        </CheatEntry>
        <CheatEntry>
          <ID>4</ID>
          <Description>"Infinite Ammo - All Weapons"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>
          </AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>5</ID>
              <Description>"Pistol 1"</Description>
              <LastState/>
              <VariableType>4 Bytes</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>784</Offset>
              </Offsets>
              <Value>999</Value>
            </CheatEntry>
            <CheatEntry>
              <ID>6</ID>
              <Description>"Pistol 2"</Description>
              <LastState/>
              <VariableType>4 Bytes</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>780</Offset>
              </Offsets>
              <Value>999</Value>
            </CheatEntry>
            <CheatEntry>
              <ID>7</ID>
              <Description>"M1 Garand"</Description>
              <LastState/>
              <VariableType>4 Bytes</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>7C8</Offset>
              </Offsets>
              <Value>999</Value>
            </CheatEntry>
            <CheatEntry>
              <ID>8</ID>
              <Description>"Thompson M1928"</Description>
              <LastState/>
              <VariableType>4 Bytes</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>7C8</Offset>
              </Offsets>
              <Value>999</Value>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>9</ID>
          <Description>"Infinite Special Ability"</Description>
          <LastState/>
          <VariableType>4 Bytes</VariableType>
          <Address>s2_mp64_ship.exe+A4B1888</Address>
          <Value>100</Value>
        </CheatEntry>
        <CheatEntry>
          <ID>10</ID>
          <Description>"Infinite Grenades"</Description>
          <LastState/>
          <VariableType>4 Bytes</VariableType>
          <Address>s2_mp64_ship.exe+A0C7388</Address>
          <Offsets>
            <Offset>768</Offset>
          </Offsets>
          <Value>99</Value>
        </CheatEntry>
        <CheatEntry>
          <ID>11</ID>
          <Description>"Player Position"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>
          </AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>12</ID>
              <Description>"X Position"</Description>
              <LastState/>
              <VariableType>Float</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>88</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>13</ID>
              <Description>"Y Position"</Description>
              <LastState/>
              <VariableType>Float</VariableType>
              <Address>s2_mp64_ship.exe+A2D7DB0</Address>
              <Offsets>
                <Offset>8C</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>14</ID>
              <Description>"Z Position"</Description>
              <LastState/>
              <VariableType>Float</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>84</Offset>
              </Offsets>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>15</ID>
          <Description>"Rapid Fire"</Description>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>[ENABLE]
// Find weapon fire rate function and patch it
aobscanmodule(rapidfire,s2_mp64_ship.exe,F3 0F 10 87 ?? ?? ?? ?? F3 0F 5C C1)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  movss xmm0,[edi+000000B4]
  mov [edi+000000B4],(float)0.01 // Very fast fire rate
  subss xmm0,xmm1

code:
  movss xmm0,[edi+000000B4]
  subss xmm0,xmm1
  jmp return

rapidfire:
  jmp newmem
  nop
  nop
return:
registersymbol(rapidfire)

[DISABLE]
rapidfire:
  movss xmm0,[edi+000000B4]
  subss xmm0,xmm1

unregistersymbol(rapidfire)
dealloc(newmem)
          </AssemblerScript>
        </CheatEntry>
        <CheatEntry>
          <ID>16</ID>
          <Description>"No Recoil"</Description>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>[ENABLE]
// Patch recoil calculation
aobscanmodule(norecoil,s2_mp64_ship.exe,F3 0F 58 ?? ?? ?? ?? ?? F3 0F 11 ?? ?? ?? ?? ??)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  // Zero out recoil values
  xorps xmm0,xmm0
  movss [edx+00000010],xmm0
  movss [edx+00000014],xmm0
  jmp return

code:
  addss xmm0,[edx+00000010]
  movss [edx+00000010],xmm0
  jmp return

norecoil:
  jmp newmem
  nop
  nop
  nop
return:
registersymbol(norecoil)

[DISABLE]
norecoil:
  addss xmm0,[edx+00000010]
  movss [edx+00000010],xmm0

unregistersymbol(norecoil)
dealloc(newmem)
          </AssemblerScript>
        </CheatEntry>
        <CheatEntry>
          <ID>17</ID>
          <Description>"Weapon Spawning"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>
          </AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>18</ID>
              <Description>"Current Weapon ID"</Description>
              <LastState/>
              <VariableType>4 Bytes</VariableType>
              <Address>97863808</Address>
              <ShowAsHex>1</ShowAsHex>
            </CheatEntry>
            <CheatEntry>
              <ID>19</ID>
              <Description>"Give Wunderwaffe DG-2"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
// Set weapon ID to Wunderwaffe
97863808:
  dd #100

[DISABLE]
// Restore original weapon
97863808:
  dd #1
              </AssemblerScript>
            </CheatEntry>
            <CheatEntry>
              <ID>20</ID>
              <Description>"Give Ray Gun"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
97863808:
  dd #101

[DISABLE]
97863808:
  dd #1
              </AssemblerScript>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>21</ID>
          <Description>"Game State Modification"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>
          </AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>22</ID>
              <Description>"Infinite Points"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
// Hook points spending function
aobscanmodule(points,s2_mp64_ship.exe,29 ?? ?? ?? ?? ?? 89 ?? ?? ?? ?? ??)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  // Don't subtract points, always add instead
  add [eax+00000010],ecx
  mov [eax+00000010],ecx
  jmp return

code:
  sub [eax+00000010],ecx
  mov [eax+00000010],ecx
  jmp return

points:
  jmp newmem
  nop
return:
registersymbol(points)

[DISABLE]
points:
  sub [eax+00000010],ecx
  mov [eax+00000010],ecx

unregistersymbol(points)
dealloc(newmem)
              </AssemblerScript>
            </CheatEntry>
            <CheatEntry>
              <ID>23</ID>
              <Description>"Freeze Round"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
// Hook round progression
aobscanmodule(round,s2_mp64_ship.exe,FF 05 ?? ?? ?? ?? 8B 0D ?? ?? ?? ??)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  // Don't increment round counter
  nop
  nop
  nop
  nop
  nop
  nop
  jmp return

code:
  inc [s2_mp64_ship.exe+????????]
  mov ecx,[s2_mp64_ship.exe+????????]
  jmp return

round:
  jmp newmem
return:
registersymbol(round)

[DISABLE]
round:
  inc [s2_mp64_ship.exe+????????]
  mov ecx,[s2_mp64_ship.exe+????????]

unregistersymbol(round)
dealloc(newmem)
              </AssemblerScript>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>24</ID>
          <Description>"Movement Enhancements"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>
          </AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>25</ID>
              <Description>"No Fall Damage"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
// Hook fall damage calculation
aobscanmodule(falldamage,s2_mp64_ship.exe,F3 0F 10 ?? ?? ?? ?? ?? F3 0F 5C ?? F3 0F 11 ??)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  // Zero out fall damage
  xorps xmm0,xmm0
  movss [eax+00000320],xmm0
  jmp return

code:
  movss xmm0,[eax+00000320]
  subss xmm0,xmm1
  movss [eax+00000320],xmm0
  jmp return

falldamage:
  jmp newmem
  nop
  nop
  nop
return:
registersymbol(falldamage)

[DISABLE]
falldamage:
  movss xmm0,[eax+00000320]
  subss xmm0,xmm1
  movss [eax+00000320],xmm0

unregistersymbol(falldamage)
dealloc(newmem)
              </AssemblerScript>
            </CheatEntry>
            <CheatEntry>
              <ID>26</ID>
              <Description>"Super Jump"</Description>
              <LastState/>
              <VariableType>Auto Assembler Script</VariableType>
              <AssemblerScript>[ENABLE]
// Modify jump height and gravity
aobscanmodule(jumpheight,s2_mp64_ship.exe,F3 0F 10 ?? ?? ?? ?? ?? F3 0F 59 ??)
alloc(newmem,$1000)

label(code)
label(return)

newmem:
  // Increase jump height by 3x
  movss xmm0,[eax+00000150]
  mov [eax+00000150],(float)3.0
  mulss xmm0,xmm1
  jmp return

code:
  movss xmm0,[eax+00000150]
  mulss xmm0,xmm1
  jmp return

jumpheight:
  jmp newmem
  nop
return:
registersymbol(jumpheight)

// Also modify gravity
aobscanmodule(gravity,s2_mp64_ship.exe,F3 0F 10 ?? ?? ?? ?? ?? F3 0F 58 ??)
alloc(gravitymem,$1000)

label(gravitycode)
label(gravityreturn)

gravitymem:
  // Reduce gravity by half
  movss xmm0,[eax+00000160]
  mov [eax+00000160],(float)0.5
  addss xmm0,xmm1
  jmp gravityreturn

gravitycode:
  movss xmm0,[eax+00000160]
  addss xmm0,xmm1
  jmp gravityreturn

gravity:
  jmp gravitymem
  nop
gravityreturn:
registersymbol(gravity)

[DISABLE]
jumpheight:
  movss xmm0,[eax+00000150]
  mulss xmm0,xmm1

gravity:
  movss xmm0,[eax+00000160]
  addss xmm0,xmm1

unregistersymbol(jumpheight)
unregistersymbol(gravity)
dealloc(newmem)
dealloc(gravitymem)
              </AssemblerScript>
            </CheatEntry>
            <CheatEntry>
              <ID>27</ID>
              <Description>"Jump Height Multiplier"</Description>
              <LastState/>
              <VariableType>Float</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>150</Offset>
              </Offsets>
              <Value>3.0</Value>
            </CheatEntry>
            <CheatEntry>
              <ID>28</ID>
              <Description>"Gravity Multiplier"</Description>
              <LastState/>
              <VariableType>Float</VariableType>
              <Address>s2_mp64_ship.exe+A0C7388</Address>
              <Offsets>
                <Offset>160</Offset>
              </Offsets>
              <Value>0.5</Value>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <UserdefinedSymbols/>
</CheatTable>
