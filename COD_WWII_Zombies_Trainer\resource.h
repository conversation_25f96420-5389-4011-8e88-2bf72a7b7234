#pragma once

// Dialog IDs
#define IDD_MAIN_DIALOG         101

// Control IDs
#define IDC_BTN_ATTACH          1001
#define IDC_CHK_GODMODE         1002
#define IDC_CHK_INFINITE_AMMO   1003
#define IDC_CHK_RAPID_FIRE      1004
#define IDC_CHK_NO_RECOIL       1005
#define IDC_CHK_AIMBOT          1006
#define IDC_BTN_ALL_PERKS       1007
#define IDC_BTN_INFINITE_POINTS 1008
#define IDC_BTN_PACK_PUNCH      1009
#define IDC_BTN_ADVANCE_ROUND   1010
#define IDC_CHK_FREEZE_ROUND    1011
#define IDC_BTN_TELEPORT_SPAWN  1012
#define IDC_EDIT_WEAPON_ID      1013
#define IDC_BTN_GIVE_WEAPON     1014
#define IDC_SLIDER_AIMBOT_FOV   1015
#define IDC_SLIDER_AIMBOT_SMOOTH 1016
#define IDC_STATIC_STATUS       1017
#define IDC_STATIC_FOV          1018
#define IDC_STATIC_SMOOTH       1019
