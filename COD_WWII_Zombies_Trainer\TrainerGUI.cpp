#include <Windows.h>
#include <CommCtrl.h>
#include <string>
#include "Memory.h"
#include "Trainer.h"
#include "resource.h"

#pragma comment(lib, "comctl32.lib")

// Global variables
Memory* g_memory = nullptr;
Trainer* g_trainer = nullptr;
HW<PERSON> g_hMainDialog = nullptr;
bool g_isInitialized = false;

// Control IDs
#define IDC_BTN_ATTACH          1001
#define IDC_CHK_GODMODE         1002
#define IDC_CHK_INFINITE_AMMO   1003
#define IDC_CHK_RAPID_FIRE      1004
#define IDC_CHK_NO_RECOIL       1005
#define IDC_CHK_AIMBOT          1006
#define IDC_BTN_ALL_PERKS       1007
#define IDC_BTN_INFINITE_POINTS 1008
#define IDC_BTN_PACK_PUNCH      1009
#define IDC_BTN_ADVANCE_ROUND   1010
#define IDC_CHK_FREEZE_ROUND    1011
#define IDC_BTN_TELEPORT_SPAWN  1012
#define IDC_EDIT_WEAPON_ID      1013
#define IDC_BTN_GIVE_WEAPON     1014
#define IDC_SLIDER_AIMBOT_FOV   1015
#define IDC_SLIDER_AIMBOT_SMOOTH 1016
#define IDC_STATIC_STATUS       1017
#define IDC_STATIC_FOV          1018
#define IDC_STATIC_SMOOTH       1019
#define IDC_CHK_NO_FALL_DAMAGE  1020
#define IDC_CHK_SUPER_JUMP      1021

// Function declarations
BOOL CALLBACK DialogProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void InitializeControls(HWND hwnd);
void UpdateStatus(const std::string& message, bool isError = false);
void UpdateFeatureStates();
DWORD WINAPI TrainerUpdateThread(LPVOID lpParam);

// Main entry point for GUI version
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES | ICC_BAR_CLASSES;
    InitCommonControlsEx(&icex);
    
    // Create main dialog
    g_hMainDialog = CreateDialog(hInstance, MAKEINTRESOURCE(IDD_MAIN_DIALOG), nullptr, DialogProc);
    if (!g_hMainDialog) {
        MessageBox(nullptr, L"Failed to create main dialog!", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    ShowWindow(g_hMainDialog, nCmdShow);
    UpdateWindow(g_hMainDialog);
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        if (!IsDialogMessage(g_hMainDialog, &msg)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
    
    // Cleanup
    if (g_trainer) {
        delete g_trainer;
    }
    if (g_memory) {
        delete g_memory;
    }
    
    return static_cast<int>(msg.wParam);
}

BOOL CALLBACK DialogProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_INITDIALOG:
            InitializeControls(hwnd);
            UpdateStatus("Ready. Click 'Attach to Game' to begin.");
            return TRUE;
            
        case WM_COMMAND:
            switch (LOWORD(wParam)) {
                case IDC_BTN_ATTACH:
                    if (!g_isInitialized) {
                        UpdateStatus("Searching for Call of Duty WWII...");
                        
                        g_memory = new Memory();
                        if (g_memory->AttachToProcess(L"s2_mp64_ship.exe")) {
                            g_trainer = new Trainer(g_memory);
                            if (g_trainer->Initialize()) {
                                g_isInitialized = true;
                                UpdateStatus("Successfully attached to Call of Duty WWII!");
                                SetWindowText(GetDlgItem(hwnd, IDC_BTN_ATTACH), L"Detach from Game");
                                
                                // Start update thread
                                CreateThread(nullptr, 0, TrainerUpdateThread, nullptr, 0, nullptr);
                            } else {
                                UpdateStatus("Failed to initialize trainer!", true);
                                delete g_trainer;
                                delete g_memory;
                                g_trainer = nullptr;
                                g_memory = nullptr;
                            }
                        } else {
                            UpdateStatus("Could not find Call of Duty WWII process!", true);
                            delete g_memory;
                            g_memory = nullptr;
                        }
                    } else {
                        // Detach
                        g_isInitialized = false;
                        if (g_trainer) {
                            delete g_trainer;
                            g_trainer = nullptr;
                        }
                        if (g_memory) {
                            delete g_memory;
                            g_memory = nullptr;
                        }
                        UpdateStatus("Detached from game.");
                        SetWindowText(GetDlgItem(hwnd, IDC_BTN_ATTACH), L"Attach to Game");
                    }
                    break;
                    
                case IDC_CHK_GODMODE:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleGodMode();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_CHK_INFINITE_AMMO:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleInfiniteAmmo();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_CHK_RAPID_FIRE:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleRapidFire();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_CHK_NO_RECOIL:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleNoRecoil();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_CHK_AIMBOT:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleAimbot();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_BTN_ALL_PERKS:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->GiveAllPerks();
                        UpdateStatus("All perks given!");
                    }
                    break;
                    
                case IDC_BTN_INFINITE_POINTS:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->GiveInfinitePoints();
                        UpdateStatus("Infinite points given!");
                    }
                    break;
                    
                case IDC_BTN_PACK_PUNCH:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->PackAPunchCurrentWeapon();
                        UpdateStatus("Current weapon Pack-a-Punched!");
                    }
                    break;
                    
                case IDC_BTN_ADVANCE_ROUND:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->AdvanceRound();
                        UpdateStatus("Round advanced!");
                    }
                    break;
                    
                case IDC_CHK_FREEZE_ROUND:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleFreezeRound();
                        UpdateFeatureStates();
                    }
                    break;

                case IDC_CHK_NO_FALL_DAMAGE:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleNoFallDamage();
                        UpdateFeatureStates();
                    }
                    break;

                case IDC_CHK_SUPER_JUMP:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->ToggleSuperJump();
                        UpdateFeatureStates();
                    }
                    break;
                    
                case IDC_BTN_TELEPORT_SPAWN:
                    if (g_trainer && g_isInitialized) {
                        g_trainer->TeleportToSpawn();
                        UpdateStatus("Teleported to spawn!");
                    }
                    break;
                    
                case IDC_BTN_GIVE_WEAPON:
                    if (g_trainer && g_isInitialized) {
                        char buffer[32];
                        GetWindowTextA(GetDlgItem(hwnd, IDC_EDIT_WEAPON_ID), buffer, sizeof(buffer));
                        int weaponID = atoi(buffer);
                        if (weaponID > 0) {
                            g_trainer->GiveWeapon(weaponID);
                            UpdateStatus("Weapon given!");
                        }
                    }
                    break;
            }
            break;
            
        case WM_HSCROLL:
            if (g_trainer && g_isInitialized) {
                HWND hSlider = reinterpret_cast<HWND>(lParam);
                int pos = SendMessage(hSlider, TBM_GETPOS, 0, 0);
                
                if (hSlider == GetDlgItem(hwnd, IDC_SLIDER_AIMBOT_FOV)) {
                    g_trainer->SetAimbotFOV(static_cast<float>(pos));
                    std::string text = "FOV: " + std::to_string(pos) + "°";
                    SetWindowTextA(GetDlgItem(hwnd, IDC_STATIC_FOV), text.c_str());
                } else if (hSlider == GetDlgItem(hwnd, IDC_SLIDER_AIMBOT_SMOOTH)) {
                    g_trainer->SetAimbotSmoothness(static_cast<float>(pos) / 10.0f);
                    std::string text = "Smoothness: " + std::to_string(pos / 10.0f);
                    SetWindowTextA(GetDlgItem(hwnd, IDC_STATIC_SMOOTH), text.c_str());
                }
            }
            break;
            
        case WM_CLOSE:
            DestroyWindow(hwnd);
            break;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
            
        default:
            return FALSE;
    }
    return TRUE;
}

void InitializeControls(HWND hwnd) {
    // Set up sliders
    HWND hFovSlider = GetDlgItem(hwnd, IDC_SLIDER_AIMBOT_FOV);
    SendMessage(hFovSlider, TBM_SETRANGE, TRUE, MAKELONG(10, 180));
    SendMessage(hFovSlider, TBM_SETPOS, TRUE, 60);
    SetWindowTextA(GetDlgItem(hwnd, IDC_STATIC_FOV), "FOV: 60°");
    
    HWND hSmoothSlider = GetDlgItem(hwnd, IDC_SLIDER_AIMBOT_SMOOTH);
    SendMessage(hSmoothSlider, TBM_SETRANGE, TRUE, MAKELONG(10, 100));
    SendMessage(hSmoothSlider, TBM_SETPOS, TRUE, 50);
    SetWindowTextA(GetDlgItem(hwnd, IDC_STATIC_SMOOTH), "Smoothness: 5.0");
    
    // Set default weapon ID
    SetWindowTextA(GetDlgItem(hwnd, IDC_EDIT_WEAPON_ID), "1");
}

void UpdateStatus(const std::string& message, bool isError) {
    if (g_hMainDialog) {
        SetWindowTextA(GetDlgItem(g_hMainDialog, IDC_STATIC_STATUS), message.c_str());
    }
}

void UpdateFeatureStates() {
    if (!g_trainer || !g_isInitialized || !g_hMainDialog) return;
    
    CheckDlgButton(g_hMainDialog, IDC_CHK_GODMODE, g_trainer->IsGodModeEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_INFINITE_AMMO, g_trainer->IsInfiniteAmmoEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_RAPID_FIRE, g_trainer->IsRapidFireEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_NO_RECOIL, g_trainer->IsNoRecoilEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_AIMBOT, g_trainer->IsAimbotEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_FREEZE_ROUND, g_trainer->IsRoundFrozen() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_NO_FALL_DAMAGE, g_trainer->IsNoFallDamageEnabled() ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(g_hMainDialog, IDC_CHK_SUPER_JUMP, g_trainer->IsSuperJumpEnabled() ? BST_CHECKED : BST_UNCHECKED);
}

DWORD WINAPI TrainerUpdateThread(LPVOID lpParam) {
    while (g_isInitialized && g_trainer) {
        g_trainer->Update();
        Sleep(16); // ~60 FPS
    }
    return 0;
}
